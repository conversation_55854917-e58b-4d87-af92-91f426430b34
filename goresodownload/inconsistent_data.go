package goresodownload

import (
	"context"
	"fmt"
	"time"

	levelStore "github.com/real-rm/golevelstore"
	"github.com/real-rm/gomongo"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// InconsistentData represents a record in the reso_photo_inconsistent table
type InconsistentData struct {
	ID            string    `bson:"_id"`           // 复合key: propId + "_" + disk
	Board         string    `bson:"board"`         // 板块类型
	PropID        string    `bson:"propId"`        // 房源ID
	Disk          string    `bson:"disk"`          // 磁盘类型 (ca6/ca7)
	ExpectedPhoLH []int32   `bson:"expectedPhoLH"` // 期望的照片哈希列表
	ExpectedDocLH []string  `bson:"expectedDocLH"` // 期望的文档哈希列表
	ExpectedTnLH  int32     `bson:"expectedTnLH"`  // 期望的缩略图哈希
	ActualPhoLH   []int32   `bson:"actualPhoLH"`   // 实际计算出的照片哈希列表
	ActualDocLH   []string  `bson:"actualDocLH"`   // 实际计算出的文档哈希列表
	ActualTnLH    int32     `bson:"actualTnLH"`    // 实际计算出的缩略图哈希
	PhoP          string    `bson:"phoP"`          // 照片路径
	Ts            time.Time `bson:"ts"`            // 创建时间戳
	Mt            time.Time `bson:"_mt"`           // 修改时间戳
}

// InconsistentDataManager manages operations on the reso_photo_inconsistent table
type InconsistentDataManager struct {
	collection *gomongo.MongoCollection
}

// NewInconsistentDataManager creates a new InconsistentDataManager instance
func NewInconsistentDataManager() *InconsistentDataManager {
	collection := gomongo.Coll("rni", "reso_photo_inconsistent")
	return &InconsistentDataManager{
		collection: collection,
	}
}

// GenerateInconsistentID generates a composite ID for inconsistent data record
func GenerateInconsistentID(propID, disk string) string {
	return fmt.Sprintf("%s_%s", propID, disk)
}

// SaveInconsistentData saves inconsistent data to the database
func (m *InconsistentDataManager) SaveInconsistentData(data *InconsistentData) error {
	if data == nil {
		return fmt.Errorf("inconsistent data cannot be nil")
	}

	ctx := context.Background()
	now := time.Now()

	// Set timestamps
	data.Mt = now
	if data.Ts.IsZero() {
		data.Ts = now
	}

	// Generate composite ID if not set
	if data.ID == "" {
		data.ID = GenerateInconsistentID(data.PropID, data.Disk)
	}

	// Use upsert to handle both insert and update cases
	filter := bson.M{"_id": data.ID}

	// Create update document with explicit field mapping
	updateDoc := bson.M{
		"_id":           data.ID,
		"board":         data.Board,
		"propId":        data.PropID,
		"disk":          data.Disk,
		"expectedPhoLH": data.ExpectedPhoLH,
		"expectedDocLH": data.ExpectedDocLH,
		"expectedTnLH":  data.ExpectedTnLH,
		"actualPhoLH":   data.ActualPhoLH,
		"actualDocLH":   data.ActualDocLH,
		"actualTnLH":    data.ActualTnLH,
		"phoP":          data.PhoP,
		"ts":            data.Ts,
		"_mt":           data.Mt,
	}

	update := bson.M{"$set": updateDoc}
	opts := options.Update().SetUpsert(true)

	_, err := m.collection.UpdateOne(ctx, filter, update, opts)
	if err != nil {
		return fmt.Errorf("failed to save inconsistent data: %w", err)
	}

	return nil
}

// GetInconsistentData retrieves inconsistent data by propID and disk
func (m *InconsistentDataManager) GetInconsistentData(propID, disk string) (*InconsistentData, error) {
	if propID == "" || disk == "" {
		return nil, fmt.Errorf("propID and disk are required")
	}

	ctx := context.Background()
	id := GenerateInconsistentID(propID, disk)

	var data InconsistentData
	err := m.collection.FindOne(ctx, bson.M{"_id": id}).Decode(&data)
	if err != nil {
		if err.Error() == "mongo: no documents in result" {
			return nil, nil // No inconsistent data found
		}
		return nil, fmt.Errorf("failed to get inconsistent data: %w", err)
	}

	return &data, nil
}

// DeleteInconsistentData removes inconsistent data record
func (m *InconsistentDataManager) DeleteInconsistentData(propID, disk string) error {
	if propID == "" || disk == "" {
		return fmt.Errorf("propID and disk are required")
	}

	ctx := context.Background()
	id := GenerateInconsistentID(propID, disk)

	_, err := m.collection.DeleteOne(ctx, bson.M{"_id": id})
	if err != nil {
		return fmt.Errorf("failed to delete inconsistent data: %w", err)
	}

	return nil
}

// ListInconsistentDataByProp retrieves all inconsistent data for a specific property
func (m *InconsistentDataManager) ListInconsistentDataByProp(propID string) ([]InconsistentData, error) {
	if propID == "" {
		return nil, fmt.Errorf("propID is required")
	}

	ctx := context.Background()
	filter := bson.M{"propId": propID}

	cursor, err := m.collection.Find(ctx, filter)
	if err != nil {
		return nil, fmt.Errorf("failed to find inconsistent data: %w", err)
	}
	defer cursor.Close(ctx)

	var results []InconsistentData
	if err := cursor.All(ctx, &results); err != nil {
		return nil, fmt.Errorf("failed to decode inconsistent data: %w", err)
	}

	return results, nil
}

// CompareMediaData compares expected and actual media data
func CompareMediaData(expected, actual []int32) bool {
	// Handle nil vs empty slice distinction
	if (expected == nil) != (actual == nil) {
		return false
	}

	if len(expected) != len(actual) {
		return false
	}

	for i, exp := range expected {
		if exp != actual[i] {
			return false
		}
	}

	return true
}

// CompareDocData compares expected and actual document data
func CompareDocData(expected, actual []string) bool {
	// Handle nil vs empty slice distinction
	if (expected == nil) != (actual == nil) {
		return false
	}

	if len(expected) != len(actual) {
		return false
	}

	for i, exp := range expected {
		if exp != actual[i] {
			return false
		}
	}

	return true
}

// IsDataConsistent checks if the media data is consistent
func IsDataConsistent(expected, actual *InconsistentData) bool {
	if expected == nil || actual == nil {
		return false
	}

	return CompareMediaData(expected.ExpectedPhoLH, actual.ActualPhoLH) &&
		CompareDocData(expected.ExpectedDocLH, actual.ActualDocLH) &&
		expected.ExpectedTnLH == actual.ActualTnLH
}

// CalculateExpectedMediaHashes calculates expected media hashes from media field
func CalculateExpectedMediaHashes(prop bson.M, analyzer *MediaDiffAnalyzer, board string) ([]int32, []string, int32, error) {
	if prop == nil {
		return nil, nil, 0, fmt.Errorf("property cannot be nil")
	}

	if analyzer == nil {
		return nil, nil, 0, fmt.Errorf("analyzer cannot be nil")
	}

	if board == "" {
		return nil, nil, 0, fmt.Errorf("board cannot be empty")
	}

	// Use getNewMediaInfo to process media information properly
	// This ensures consistent handling of different board types and media filtering
	mediaInfo, err := analyzer.getNewMediaInfo(prop, board)
	if err != nil {
		return nil, nil, 0, fmt.Errorf("failed to get media info: %w", err)
	}

	if len(mediaInfo.NewMediaList) == 0 {
		// No media found, return empty lists
		return []int32{}, []string{}, 0, nil
	}

	// Use analyzer to generate hash lists from processed media
	phoLH, docLH := analyzer.generateHashList(mediaInfo.NewMediaList)

	// Calculate thumbnail hash based on first photo's MediaKey (same logic as in generateThumbnailToCache)
	var tnLH int32 = 0
	if mediaInfo.NewFirstPic.MediaKey != "" {
		tnLH = int32(levelStore.MurmurToInt32(mediaInfo.NewFirstPic.MediaKey + "-t"))
	}

	return phoLH, docLH, tnLH, nil
}

// CheckDataConsistencyAndHandle checks data consistency and handles inconsistent data
func CheckDataConsistencyAndHandle(result *AnalysisResult, thumbnailHash int32, board, diskType string, analyzer *MediaDiffAnalyzer, inconsistentManager *InconsistentDataManager, downloadQueueCa6, downloadQueueCa7 *ResourceDownloadQueue, downloader *Downloader) error {
	if result == nil {
		return fmt.Errorf("result cannot be nil")
	}

	// Get the merged document to extract media field
	tableName, exists := BoardMergedTable[board]
	if !exists {
		return fmt.Errorf("unknown board type: %s", board)
	}

	mergedCol := gomongo.Coll("rni", tableName)
	var mergedDoc bson.M
	err := mergedCol.FindOne(context.Background(), bson.M{"_id": result.ID}).Decode(&mergedDoc)
	if err != nil {
		return fmt.Errorf("failed to get merged document: %w", err)
	}

	// Calculate expected values from media field
	expectedPhoLH, expectedDocLH, expectedTnLH, err := CalculateExpectedMediaHashes(mergedDoc, analyzer, board)
	if err != nil {
		return fmt.Errorf("failed to calculate expected media hashes: %w", err)
	}

	// Actual values from download result
	actualPhoLH := result.PhoLH
	actualDocLH := result.DocLH
	actualTnLH := thumbnailHash

	// Check consistency
	isConsistent := CompareMediaData(expectedPhoLH, actualPhoLH) &&
		CompareDocData(expectedDocLH, actualDocLH) &&
		expectedTnLH == actualTnLH

	if !isConsistent {
		// Data is inconsistent - save to inconsistent table and re-queue
		return handleInconsistentData(result, expectedPhoLH, expectedDocLH, expectedTnLH,
			actualPhoLH, actualDocLH, actualTnLH, board, diskType,
			inconsistentManager, downloadQueueCa6, downloadQueueCa7)
	}

	// Data is consistent - update merged table with phoDlOs and clean up inconsistent records
	return handleConsistentData(result, thumbnailHash, board, diskType, inconsistentManager, downloader)
}

// handleInconsistentData handles inconsistent data by saving to inconsistent table and re-queuing
func handleInconsistentData(result *AnalysisResult, expectedPhoLH []int32, expectedDocLH []string, expectedTnLH int32,
	actualPhoLH []int32, actualDocLH []string, actualTnLH int32, board, diskType string,
	inconsistentManager *InconsistentDataManager, downloadQueueCa6, downloadQueueCa7 *ResourceDownloadQueue) error {

	// Get phoP from result
	phoP, err := GetPhoP(bson.M{"_id": result.ID}, result.ID, board)
	if err != nil {
		return fmt.Errorf("failed to get phoP: %w", err)
	}

	// Create inconsistent data record
	inconsistentData := &InconsistentData{
		Board:         board,
		PropID:        result.ID,
		Disk:          diskType,
		ExpectedPhoLH: expectedPhoLH,
		ExpectedDocLH: expectedDocLH,
		ExpectedTnLH:  expectedTnLH,
		ActualPhoLH:   actualPhoLH,
		ActualDocLH:   actualDocLH,
		ActualTnLH:    actualTnLH,
		PhoP:          phoP,
	}

	// Save to inconsistent table
	if err := inconsistentManager.SaveInconsistentData(inconsistentData); err != nil {
		return fmt.Errorf("failed to save inconsistent data: %w", err)
	}

	// Re-add to both queues with high priority
	priority := int(10000) // High priority for inconsistent data
	var addErrors []error

	// Add to ca6 queue
	if downloadQueueCa6 != nil {
		if err := downloadQueueCa6.AddToQueue(result.ID, priority, board, phoP); err != nil {
			addErrors = append(addErrors, fmt.Errorf("ca6 queue error: %w", err))
		}
	}

	// Add to ca7 queue
	if downloadQueueCa7 != nil {
		if err := downloadQueueCa7.AddToQueue(result.ID, priority, board, phoP); err != nil {
			addErrors = append(addErrors, fmt.Errorf("ca7 queue error: %w", err))
		}
	}

	if len(addErrors) > 0 {
		return fmt.Errorf("failed to re-add to queues: %v", addErrors)
	}

	return nil
}

// handleConsistentData handles consistent data by cleaning up inconsistent records
// Note: phoDlOs field is already updated by ProcessAnalysisResult
func handleConsistentData(result *AnalysisResult, thumbnailHash int32, board, diskType string, inconsistentManager *InconsistentDataManager, downloader *Downloader) error {
	// Delete any existing inconsistent record for this prop and disk
	if err := inconsistentManager.DeleteInconsistentData(result.ID, diskType); err != nil {
		// Log error but don't fail the process
		return fmt.Errorf("failed to delete inconsistent data: %w", err)
	}

	return nil
}
